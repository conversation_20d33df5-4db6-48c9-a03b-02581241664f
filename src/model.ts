import type { FormInstance } from 'antd'
import type React from 'react'

export interface Field {
  label: string
  values?: Value[]
}

export interface Value {
  label: string
  value: string
}

export interface BusinessArea {
  businessArea: string
  usageType: string
}

export interface DeepLinkArgs {
  packagedByGroupIT?: boolean
  externalApplication?: boolean
  italy?: boolean
}

export interface DesktopSoftwareAppData {
  id: string
  guiType: string[]
  ring: number
  description: string
  applicationsOwners: Value[]
  applicationResponsibles: Value[]
}

export interface ServerSoftware {
  id: string
  name: string
  description: string
  appReference: string
  provider: string
  techCategory?: string
  businessAreas: BusinessArea[]
  phaseIn?: string
  active?: string
  phaseOut?: string
  applicationOwners: string[]
}

export interface DesktopSoftware {
  name: string
  active: string
  description: string
  subcategory: string
  guiType: string
  ring: string
  orderable: string
  packagedByGroupIT: boolean
  softwareProfile: string[]
  businessAreaOwner: string
  businessAreaUser: string[]
  componentOwners: string[]
  componentResponsibles: string[]
  creator: lxr.ReportSetupSettings['currentUser']
  comment: string
  done: boolean
  transferDone: boolean
  fsType: WizzardType
  appReference: string
  desktopSoftwareReferences: string[]
}

export interface Application {
  prefix: string
  name: string
  description: string
  plattform: string
  providerName: string
  sourcing: string
  softwareType: string
  guiTypes: string[]
  url: string
  phaseIn: string
  active: string
  phaseOut: string
  applicationDomain: string
  businessCapabilities: string
  businessAreaOwner: string
  businessAreaUsers: string[]
  userAdmin: string
  commentOnUserAdmin: string
  userAccessControl: string
  commentOnUserAccessControl: string
  reachableFromNonManagedDevice: string
  multiFactorAuthentication: string
  multiFactorAuthenticationComment: string
  // needsMFA: string;

  applicationOwnerTechnical: string
  applicationResponsibleFunctional: string
  saaSClassification: string
  saaSClassificationComment: string
  creator: lxr.ReportSetupSettings['currentUser']
  comment: string
  done: boolean
  transferDone: boolean
  fsType: WizzardType
}

export interface FormCheck {
  createITComponent: boolean
  useExistingITComponent: boolean
  createProvider: boolean
  useExistingProvider: boolean
}

export interface LXDocument {
  id: string
  name: string
  documentType: string
  description: string
  createdAt: string
  application: Application
}

export interface GenericObject {
  [key: string]: any
}

export type WizzardType = 'Application' | 'ITComponent'

export interface FactSheetData {
  [key: string]: any
}

export type ReportUser = lxr.ReportSetupSettings['currentUser'] &
  { swissUser: boolean, admin: boolean, member:boolean }

export interface WizardFormModel {
  id: string // ALSO USED FOR SLUG
  name: string
  addWithTemporaryMode: boolean
  originFactSheet: string
  steps: WizardStep[]
  onlyAdmin: boolean
  init: (form: FormInstance, currentUser: ReportUser, transferData: {
    [key: string]: any
  } | undefined, factSheetId?: string | undefined, validate?: () => Promise<boolean>, deepLinkArgs?: DeepLinkArgs, setNewCustomProviders?: React.Dispatch<React.SetStateAction<Value[]>>) => void
  save: (data: FactSheetData, currentUser: ReportUser, docId: string | undefined) => Promise<string | undefined>
  saveDirect?: (data: FactSheetData, currentUser: ReportUser) => Promise<string | undefined> // ONLY RELEVANT WHEN addWithTemporaryMode = true;
  saveSuccessMessage: string
  saveErrorMessage: string
}

export type WizardRule = (form: FormInstance, steps: WizardStep[], currentStep: number, currentUser: ReportUser, update: (v: WizardStep[]) => void, deepLinkArgs: DeepLinkArgs) => void
export type WizardStepCheck = (form: FormInstance, currentUser: ReportUser, step: WizardStep) => Promise<string | undefined>

export interface WizardStep {
  name: string
  fields: WizardField[]
  customChecks: WizardStepCheck
  rules: WizardRule[]
}

export interface WizardField {
  title: string
  description: string
  name: string
  loadLeanIXOptions?: boolean
  loadFactSheet?: LoadingOptions | undefined
  loadTag?: string | undefined
  required: boolean
  fieldType: WizardFieldType
  addonBefore?: string | undefined
  visible: boolean
  options?: { label: string, value: string }[]
  disabled?: boolean
  update?: void
}

export type LoadingOptions =
  'Domain'
  | 'BusinessCapability'
  | 'UserGroup'
  | 'Application'
  | 'Interface'
  | 'Provider'
  | 'ProviderId'
  | 'DesktopSoftware'
  | 'Middleware'

export type WizardFieldType =
  'SingleSelect'
  | 'MultiSelect'
  | 'MultiSelectProvider'
  | 'SingleSelectProvider'
  | 'Text'
  | 'NameField'
  | 'ServerSoftwareName'
  | 'UserSelect'
  | 'UserSelectMultiple'
  | 'Switch'
  | 'TextArea'
  | 'DatePicker'
  | 'AppReferenceWithSuggestionDSW' // Desktop Software
  | 'AppReferenceWithSuggestionSSW' // Server Software
  | 'Hidden'
  | 'ExpectedSteps'
  | 'ReadOnlyComment'
  | 'AutoCompleteField'
  | 'BusinessAreaSelection'
  | 'SelectFactsheetTable'
  | 'ApplicationSelect'

export interface Role {
  name: string
  id: string
}

export interface User {
  email: string
  id: string
}

export interface Node {
  id: string
  user: User
  roles: Role[]
}

export interface Edge {
  node: Node
}

export interface Subscriptions {
  edges: Edge[]
}

export interface RelationValue {
  relationId: string
  factSheetId: string
}
