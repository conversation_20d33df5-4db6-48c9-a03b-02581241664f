import type { FormInstance } from 'antd'
import type { GenericObject, Subscriptions, WizardFormModel, WizardStep } from './model'
import dayjs from 'dayjs'
import { PROD } from './constants'

export function convertDatesInObject(obj: GenericObject): GenericObject {
  const convertedObj: GenericObject = {}
  for (const key in obj) {
    const value = obj[key]
    if (typeof value === 'string' && dayjs(value).isValid() && ['phaseIn', 'active', 'phaseOut'].includes(key)) {
      convertedObj[key] = dayjs(value)
    }
    else {
      convertedObj[key] = value
    }
  }
  return convertedObj
}

export const getRingByBusinessCriticality = (businessCriticality: string) => {
  if (businessCriticality === 'businessOperational') {
    return 1
  }
  else if (businessCriticality === 'businessCritical') {
    return 2
  }
  else if (businessCriticality === 'missionCritical') {
    return 3
  }
  else if (businessCriticality === 'administrativeService') {
    return 4
  }
  else {
    return -1
  }
}

export const extractDomainEnding = (email: string) => {
  const parts = email.split('@')
  // Checking if the email has the required format
  if (parts.length === 2) {
    // Splitting the domain at the last dot
    const domainParts = parts[1].split('.')
    const domainEnding = domainParts[domainParts.length - 1]
    // Returning the domain ending
    return domainEnding.toLowerCase()
  }
  else {
    // Returning an empty string if the email format is not valid
    return ''
  }
}

export const extractUseCaseAndIdFromURL = (url: string): { usecase: string, id: string, packagedByGroupIT: string, externalApplication: boolean, italy: boolean } | null => {
  const urlParams = new URLSearchParams(url.split('?')[1])

  const usecase = urlParams.get('usecase')
  const id = urlParams.get('id')
  const packagedByGroupIT = urlParams.get('packagedByGroupIT')
  const externalApplication = urlParams.get('externalApplication')
  const italy = urlParams.get('italy')

  if (usecase || id || packagedByGroupIT || externalApplication || italy) {
    return {
      usecase: usecase || '',
      id: id || '',
      packagedByGroupIT: packagedByGroupIT || '',
      externalApplication: externalApplication === 'true',
      italy: italy === 'true'
    }
  }
  else {
    return null
  }
}
export const removeSearchParameters = (url: string): string => {
  if (url) {
    const urlObject = new URL(url)
    urlObject.search = ''
    return urlObject.toString()
  }
  else {
    return ''
  }
}

export const setRouterLink = (value: string) => {
  if (PROD) {
    lx.openRouterLink(value)
  }
  else {
    console.log('no router link on local environment')
  }
}

export const getAllFieldValues = (formInstance: FormInstance, form: WizardFormModel) => {
  const allFields = form.steps.map(step => step.fields
    .filter(f => f.visible || f.fieldType === 'Hidden')
    .map(fi => (fi.name))).flat()

  return formInstance.getFieldsValue([...allFields])
}

export const getFieldByName = (newSteps: WizardStep[], currentStep: number, name: string) => {
  if (newSteps[currentStep].fields.filter(f => f.name === name).length > 0) {
    return { ...newSteps[currentStep].fields.filter(f => f.name === name)[0] }
  }
  else { throw new Error('NOT FOUND') }
}

export const getAllRoleIdsByUserId = (subscriptions: Subscriptions, userId: string) => {
  const roleIds: string[] = []
  let subId = ''

  for (const edge of subscriptions.edges) {
    const { user, roles, id } = edge.node
    if (user.id === userId) {
      subId = id
      for (const role of roles) {
        roleIds.push(role.id)
      }
    }
  }

  return {
    subId,
    roleIds
  }
}

// Funktion zur Erkennung von UUIDs
export const isUUID = (value: string | undefined) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  return value ? uuidRegex.test(value) : false
}
