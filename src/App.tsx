import type { Application, DeepLinkArgs, DesktopSoftware, WizardFormModel } from './model'
import { Button, Col, Form, Row, Segmented, Skeleton, Space, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import BacklogContainer from './components/admin/BacklogContainer'
import FactsheetTypeChooser from './components/form/FactsheetTypeChooser'
import WizardForm from './components/form/WizardForm'
import { addApplicationForm } from './config/addApplication'
import { addDesktopSoftwareForm } from './config/addDesktopSoftware'
import { addServerSoftware } from './config/addServerSoftware'
import { checkApplicationForm } from './config/applicationCheck'
import { addAPI } from './config/information-flow/api'
import { addKafkaConsumer } from './config/information-flow/kafkaConsumer'
import { addKafkaInterface } from './config/information-flow/kafkaInterface'
import { addMFT } from './config/information-flow/mft'
import { WIZARDS } from './config/settings'
import { transferApplicationForm } from './config/transferApplication'
import {
  extractDomainEnding,
  extractUseCaseAndIdFromURL,
  getAllFieldValues,
  removeSearchParameters,
  setRouterLink
} from './helpers'
import { useDocuments } from './hooks/documents'
import { useLeaniX } from './hooks/leanix'
import './App.scss'
import '@leanix/reporting'

function App() {
  const [formInstance] = Form.useForm()
  const [form, setForm] = useState<WizardFormModel | undefined>(undefined)
  const [currentStep, setCurrentStep] = useState<number>(0)

  const { loadDocuments, updateRessource } = useDocuments()
  const { getAppDataById,getApplicationDetails } = useLeaniX()

  const wizards = WIZARDS

  const [loading, setLoading] = useState(true)
  const [adminMode, setAdminMode] = useState(false)

  const [transferFactsheet, setTransferFactsheet] = useState<{ [key: string]: any } | undefined>(undefined)
  const [transferDocId, setTransferDocId] = useState<string | undefined>(undefined)

  const [currentUser, setCurrentUser] = useState<lxr.ReportSetupSettings['currentUser'] | undefined>(undefined)

  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const [isMember, setIsMember] = useState<boolean>(false)
  const [swissUser, setSwissUser] = useState<boolean>(true)

  const [deepLinkArgs, setDeepLinkArgs] = useState<DeepLinkArgs>({})

  const [showSuccessMessage, setShowSuccessMessage] = useState(false)

  const initializeReport = async () => {
    const reportSetup = await lx.init()
    // console.log("REPORT SETTINGS", reportSetup)
    setCurrentUser(reportSetup.settings.currentUser)
    // console.log(reportSetup.settings.currentUser.email)

    console.log("CURRENT USER:", reportSetup.settings.currentUser)

    const email = reportSetup.settings.currentUser.email
    const isSwissUser = extractDomainEnding(email) === 'ch'
    setSwissUser(isSwissUser)

    // console.log(email, isSwissUser)

    const response = await lx.executeParentOriginXHR('GET', `https://helvetia.leanix.net/services/mtm/v1/users/${reportSetup.settings.currentUser.id}/permissions`)

    const data = JSON.parse(response.body)
    console.log(data)



    let hasRights = false
    let hasAdmin = false
    let hasMember = false

    if (data && data.data.length > 0) { // applikationen nur member und desktop software alle!
      const role = data.data[0].role
      const customerRoles = data.data[0].customerRoles

      hasRights = role === 'ADMIN' || role === 'MEMBER' || customerRoles.includes('EAM')
      hasAdmin = role === 'ADMIN' || customerRoles.includes('EAM')
      hasMember = role === 'MEMBER'

      // console.log(role);
      if (hasAdmin) {
        setIsAdmin(true)
        setAdminMode(true)
        setIsMember(true)
      }
      else if (hasMember) {
        setIsMember(true)
        setIsAdmin(false)
        setAdminMode(false)
      }
      else {
        setIsAdmin(false)
        setAdminMode(false)
      }
    }

    // CHECK DEEPLINKS
    if (document.referrer) {
      const data = extractUseCaseAndIdFromURL(document.referrer)
      // console.log("START HANDLING DEEPLINKS", data, isMember, isAdmin, hasRights);

      // Check if user is responsible for the application
      let isResponsible = false;

      if (data && data.id) {
        try {
          const appDetails:any = await getApplicationDetails(data.id);
          if (appDetails) {
            const allowedPeople = [...appDetails.applicationOwnerTechnical || [], ...appDetails.applicationResponsibleFunctional || []];
            isResponsible = allowedPeople.includes(reportSetup.settings.currentUser.id);
          }
        } catch (error) {
          console.log('Error checking responsibility:', error);
        }
      }

      if (data && data.usecase.length > 0 && (!hasRights && !isResponsible || (data.usecase === transferApplicationForm.id && !hasAdmin && !isResponsible))) {
        lx.showToastr('error', 'You do not have the necessary permissions')
      }

      if (data && (hasRights || isResponsible)) {
        await initRouterLinks(data.usecase, data.id, data.packagedByGroupIT, data.externalApplication, data.italy)
      }
    }

    lx.ready({
      facets: []
    })
  }

  const initRouterLinks = async (useCase: string, id?: string | undefined, packagedByGroupIT?: string | undefined, externalApplication?: boolean | undefined, italy?: boolean | undefined) => {
    if (useCase === checkApplicationForm.id) {
      let valid = true

      if (!id) { valid = false }

      // CHECK IF APP EXISTS
      await getAppDataById(id!).then(result => console.log(result)).catch((err) => {
        console.log(err)
        valid = false
      })

      if (!valid) {
        if (id) {
          lx.showToastr('error', 'No application found with this ID')
        }
        setForm(checkApplicationForm)
        setAdminMode(false)
        updateRouterLink(checkApplicationForm.id, undefined, undefined, undefined, undefined)
      }
      else {
        setTransferDocId(id)
        setForm(checkApplicationForm)
        setAdminMode(false)
        updateRouterLink(checkApplicationForm.id, id, undefined, undefined, undefined)
      }
    }
    else if (useCase === addApplicationForm.id) {
      if (externalApplication) {
        setDeepLinkArgs(prev => ({
          ...prev,
          externalApplication
        }))
      }

      setForm(addApplicationForm)
      setAdminMode(false)
      updateRouterLink(addApplicationForm.id, undefined, undefined, externalApplication, undefined)
    }
    else if (useCase === addDesktopSoftwareForm.id) {
      if (packagedByGroupIT) {
        setDeepLinkArgs(prev => ({
          ...prev,
          packagedByGroupIT: (String(packagedByGroupIT).toLowerCase() === 'true')
        }))
      }
      setForm(addDesktopSoftwareForm)
      setAdminMode(false)
      updateRouterLink(addDesktopSoftwareForm.id, undefined, packagedByGroupIT, undefined, undefined)
    }
    else if (useCase === transferApplicationForm.id) {
      const documents = await loadDocuments()
      const relevant = documents.filter(doc => doc.id === id)
      if (relevant.length > 0) {
        initTransfer(JSON.parse(relevant[0].description), relevant[0].id)
      }
      else {
        lx.showToastr('error', 'No transfer application found with the given id')
      }
    }
    else if (useCase === addServerSoftware.id) {
      if (italy) {
        setDeepLinkArgs(prev => ({
          ...prev,
          italy
        }))
      }
      setForm(addServerSoftware)
      setAdminMode(false)
      updateRouterLink(addServerSoftware.id, undefined, undefined, undefined, italy)
    }
    else if (useCase === addMFT.id) {
      setForm(addMFT)
      setAdminMode(false)
      updateRouterLink(addMFT.id, undefined, undefined, undefined, undefined)
    }
    else if (useCase === addAPI.id) {
      setForm(addAPI)
      setAdminMode(false)
      updateRouterLink(addAPI.id, undefined, undefined, undefined, undefined)
    }
    else if (useCase === addKafkaInterface.id) {
      setForm(addKafkaInterface)
      setAdminMode(false)
      updateRouterLink(addKafkaInterface.id, undefined, undefined, undefined, undefined)
    }
    else if (useCase === addKafkaConsumer.id) {
      setForm(addKafkaConsumer)
      setAdminMode(false)
      updateRouterLink(addKafkaConsumer.id, undefined, undefined, undefined, undefined)
    }
  }

  const updateRouterLink = (formId: string, id?: string | undefined, packagedByGroupIT?: string | undefined, externalApplication?: boolean | undefined, italy?: boolean | undefined): void => {
    setRouterLink(`${removeSearchParameters(document.referrer)}?usecase=${formId}${id ? `&id=${id}` : ''}${packagedByGroupIT ? `&packagedByGroupIT=${packagedByGroupIT}` : ''}${externalApplication ? `&externalApplication=${externalApplication}` : ''}${italy ? `&italy=${italy}` : ''}`)
  }

  const initTransfer = (factsheet: Application | DesktopSoftware, docId: string) => {
    setForm(transferApplicationForm)
    setTransferFactsheet(factsheet)
    setTransferDocId(docId)
    setAdminMode(false)
    updateRouterLink(transferApplicationForm.id, docId, undefined, undefined, undefined)
  }

  const cancelTransfer = () => {
    formInstance.resetFields()
    setCurrentStep(0)
    setTransferFactsheet(undefined)
    setTransferDocId(undefined)
    setAdminMode(true)
    setForm(undefined)
    setRouterLink(`${removeSearchParameters(document.referrer)}`)
  }

  const goBack = () => {
    formInstance.resetFields()
    setCurrentStep(0)
    setTransferFactsheet(undefined)
    setTransferDocId(undefined)
    setForm(undefined)
    setRouterLink(`${removeSearchParameters(document.referrer)}`)
  }

  const saveChanges = async () => {
    // const get orignal document

    // console.log("save changes with", [...form!.steps[currentStep].fields.map((f) => (f.name))])
    const dataToProcess = getAllFieldValues(formInstance, form!)
    const application = {
      ...dataToProcess,
      creator: transferFactsheet?.creator,
      fsType: 'Application',
      updatedBy: currentUser,
      updatedAt: new Date()
    }

    setLoading(true)
    const result = await updateRessource(transferDocId!, application)
    // console.log(result);
    if (result && result.result && result.result.id) {
      lx.showToastr('success', 'Changes successfully saved!')
    }
    else {
      lx.showToastr('error', 'Error while saving the changes. Please try it again!')
    }
    formInstance.resetFields()
    setCurrentStep(0)
    cancelTransfer()
    setLoading(false)
  }

  useEffect(() => {
    initializeReport().then(() => {
      setLoading(false)
    })
  }, [])

  const { Title } = Typography

  const name = form?.name.includes('Application')
    ? `${formInstance.getFieldValue('prefix') || ''}${formInstance.getFieldValue('name') || ''} ${formInstance.getFieldValue('external') ? ' (ext.)' : ''}`.trim()
    : `${formInstance.getFieldValue('prefix') || ''}${formInstance.getFieldValue('name') || ''} ${formInstance.getFieldValue('suffix') || ''}`.trim()

  return (
    <Row style={{ marginRight: '8px' }}>
      <Col span={12} md={12} xs={24}>
        <Space>
          {(!adminMode && !transferFactsheet && form) && (
            <Button size="small" onClick={() => goBack()}>Back</Button>)}
          <Title
            level={3}
          >
            {adminMode
              ? 'Backlog'
              : form
                ? (
                    <>
                      {form.name}
                      {' '}
                      {name.length > 2 ? '- ' : ''}
                      <span
                        style={{ textDecoration: 'underline' }}
                      >
                        {name.length > 2 ? name : ''}
                      </span>
                    </>
                  )
                : 'Add a Factsheet'}
            {' '}
            {transferFactsheet
              ? (
                  <Space style={{ marginLeft: '16px' }}>
                    <Button onClick={() => saveChanges()} size="small">Save Changes</Button>
                    <Button onClick={() => cancelTransfer()} size="small" danger>Cancel</Button>
                  </Space>
                )
              : undefined}
            {' '}

          </Title>
        </Space>

      </Col>
      {loading
        ? <Skeleton active />
        : (
            <>
              {isAdmin
                ? (
                    <Col xs={24} md={12} span={12} style={{ display: 'flex', justifyContent: 'end' }}>
                      <Space>
                        <Segmented<string>
                          style={{ marginBottom: '8px' }}
                          value={adminMode ? 'Backlog' : 'Add Factsheet'}
                          options={['Backlog', 'Add Factsheet']}
                          onChange={(value) => {
                            if (value === 'Backlog') { cancelTransfer() }
                            setAdminMode(value === 'Backlog')
                          }}
                        />
                      </Space>
                    </Col>
                  )
                : undefined}

              {adminMode
                ? (
                    <Col span={24}>
                      <BacklogContainer initTransfer={initTransfer} />
                    </Col>
                  )
                : (
                    <Col span={24}>
                      {form === undefined
                        ? (
                            <FactsheetTypeChooser
                              showSuccessMessage={showSuccessMessage}
                              isMember={isMember}
                              setSelectedWizard={(returnedForm) => {
                                if (returnedForm) { updateRouterLink(returnedForm.id, undefined, undefined, undefined, undefined) }
                                setForm(returnedForm)
                              }}
                              wizards={wizards}
                              setShowSuccessMessage={setShowSuccessMessage}
                            />
                          )
                        : undefined}
                      {(!loading && form && currentUser)
                      && (
                        <WizardForm
                          currentStep={currentStep}
                          setCurrentStep={setCurrentStep}
                          form={form!}
                          setForm={setForm}
                          showSuccessMessage={setShowSuccessMessage}
                          transferData={transferFactsheet}
                          transferDocId={transferDocId}
                          formInstance={formInstance}
                          currentUser={{ ...currentUser!, swissUser, admin: isAdmin, member:isMember }}
                          resetForm={() => setForm(undefined)}
                          cancelTransfer={cancelTransfer}
                          deepLinkArgs={deepLinkArgs}
                        />
                      )}
                    </Col>
                  )}
            </>
          )}
    </Row>
  )
}

export default App
