import { Form, Input, Select, Space } from 'antd'
import React from 'react'

const corpUnits = [
  {
    value: '(AT)',
    label: '(AT)'
  },
  {
    value: '(CAS)',
    label: '(CAS)'
  },
  {
    value: '(CF)',
    label: '(CF)'
  },
  {
    value: '(CH)',
    label: '(CH)'
  },
  {
    value: '(DE)',
    label: '(DE)'
  },
  {
    value: '(ES)',
    label: '(ES)'
  },
  {
    value: '(FR)',
    label: '(FR)'
  },
  {
    value: '(ITA)',
    label: '(ITA)'
  }
]

export default function ServerSoftwareName({ form, ...props }) {
  const italyComponent = form?.getFieldValue('italyComponent')

  // Only ITA option when Italy Component is enabled
  const italyOptions = [
    {
      value: '(ITA)',
      label: '(ITA)'
    }
  ]

  return (
    <Space.Compact style={{ minWidth: '100%' }}>
      <Form.Item
        style={{ marginBottom: 0 }}
        className="name-input"
        name="name"
        rules={[{ required: props.required, message: '' }]}
      >
        <Input disabled={props.disabled} />
      </Form.Item>
      {italyComponent && (
        <Form.Item
          style={{ marginBottom: 0 }}
          name="suffix"
          rules={[{ required: false, message: 'Please select a suffix!' }]}
        >
          <Select
            disabled={true}
            style={{ width: '90px' }}
            options={italyOptions}
            defaultValue="(ITA)"
          />
        </Form.Item>
      )}
    </Space.Compact>
  )
}
